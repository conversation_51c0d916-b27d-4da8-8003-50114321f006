import os
import logging
import re
from typing import List, Dict, Any
from urllib.parse import urlparse
from yt_dlp import YoutubeDL

logger = logging.getLogger(__name__)


class MediaService:
    def _sanitize_filename(self, url: str) -> str:
        """
        Convert URL to a safe folder name by removing invalid characters
        and taking the last meaningful part of the URL.
        """
        parsed = urlparse(url)
        path_segments = [seg for seg in parsed.path.split('/') if seg]
        base_name = path_segments[-1] if path_segments else 'download'
        
        sanitized = re.sub(r'[^\w\-_]', '_', base_name)
        return sanitized or 'download'

    def _create_media_directory(self, base_dir: str = None) -> str:
        """
        Create and return the path to media directory.
        
        Args:
            base_dir (str, optional): Base directory path. If None, uses current directory.
            
        Returns:
            str: Path to the media directory.
        """
        if base_dir is None:
            base_dir = os.path.join(os.getcwd(), 'media')
        os.makedirs(base_dir, exist_ok=True)
        return base_dir

    def _create_url_directory(self, media_dir: str, url: str) -> str:
        """
        Create and return a unique directory for the URL.
        
        Args:
            media_dir (str): Base media directory path.
            url (str): URL being processed.
            
        Returns:
            str: Path to the URL-specific directory.
        """
        url_folder_name = self._sanitize_filename(url)
        url_folder_path = os.path.join(media_dir, url_folder_name)
        
        counter = 1
        original_path = url_folder_path
        while os.path.exists(url_folder_path):
            url_folder_path = f"{original_path}_{counter}"
            counter += 1
        
        os.makedirs(url_folder_path, exist_ok=True)
        logger.info(f"Created directory for media: {url_folder_path}")
        return url_folder_path

    def _get_ydl_options(self, output_template: str, format: str) -> Dict[str, Any]:
        """
        Configure and return yt-dlp options.
        
        Args:
            output_template (str): Template for output filenames.
            format (str): Desired format for downloads.
            
        Returns:
            Dict[str, Any]: yt-dlp options dictionary.
        """
        return {
            'outtmpl': output_template,
            'format': format,
            'noplaylist': False,
            'progress_hooks': [self._progress_hook],
            'logger': logger,
            'merge_output_format': 'mp4',
            'write_thumbnail': True,
        }

    def _progress_hook(self, d: Dict[str, Any]) -> None:
        """
        Handle download progress updates.
        
        Args:
            d (Dict[str, Any]): Progress information from yt-dlp.
        """
        if d.get('status') == 'finished':
            filename = d.get('filename')
            if filename and os.path.exists(filename):
                self._downloaded_files.append(filename)
                logger.info(f"Downloaded: {filename}")

    def _process_playlist(self, ydl: YoutubeDL, info: Dict[str, Any]) -> None:
        """
        Process playlist entries if present.
        
        Args:
            ydl (YoutubeDL): YoutubeDL instance.
            info (Dict[str, Any]): Extracted information.
        """
        if 'entries' in info:
            for entry in info['entries']:
                if entry and 'url' in entry:
                    ydl.download([entry['url']])

    def download(
        self,
        url: str,
        output_dir: str = None,
        format: str = 'bestvideo+bestaudio/best/jpg',
    ) -> List[str]:
        """
        Downloads media files (videos and images) from the given URL and returns the paths of the downloaded files.
        Creates a separate folder for each URL inside the media directory.

        Args:
            url (str): The media URL to download (e.g., Instagram reel or post URL).
            output_dir (str, optional): Base directory path where media folders will be created. Default: current directory.
            format (str, optional): Download format (e.g., 'best', 'bestaudio', '720p'). Default: 'bestvideo+bestaudio/best'.

        Returns:
            List[str]: List of full paths to the downloaded files.

        Raises:
            ValueError: Invalid or missing URL.
            RuntimeError: If an error occurs during download process.
        """
        if not url or not isinstance(url, str):
            logger.error("URL is invalid or empty.")
            raise ValueError("A valid URL must be provided.")

        self._downloaded_files: List[str] = []

        media_dir = self._create_media_directory(output_dir)
        url_dir = self._create_url_directory(media_dir, url)
        output_template = os.path.join(url_dir, '%(title)s-%(id)s.%(ext)s')

        ydl_opts = self._get_ydl_options(output_template, format)

        try:
            logger.info(f"Downloading media from {url}...")
            with YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)
                self._process_playlist(ydl, info)

            absolute_paths = [os.path.abspath(f) for f in self._downloaded_files if os.path.exists(f)]
            if not absolute_paths:
                logger.warning("No files were downloaded.")
                return []

            logger.info(f"Download completed successfully. Files: {absolute_paths}")
            return absolute_paths

        except Exception as e:
            logger.error(f"An error occurred during download: {e}")
            raise RuntimeError(f"Error downloading media: {e}")


media_service = MediaService()
